using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class CreateThamSoHeThongCommand : IRequest<Unit>
    {
        public CreateThamSoHeThongModel Model { get; set; }

        /// <summary>
        /// Thêm mới tham số hệ thống
        /// </summary>
        /// <param name="model">Thông tin tham số hệ thống cần thêm mới</param>
        public CreateThamSoHeThongCommand(CreateThamSoHeThongModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateThamSoHeThongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateThamSoHeThongCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {ThamSoHeThongConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateThamSoHeThongModel, HtThamSoHeThong>(model);

                var checkId = await _dataContext.HtThamSoHeThongs.AnyAsync(x => x.IdThamSo == entity.IdThamSo && x.IdPh == entity.IdPh);
                if (checkId)
                {
                    throw new ArgumentException($"{_localizer["tham-so-he-thong.id-tham-so.existed"]}");
                }

                entity.UserName = _contextAccessor.UserName;
                entity.DateModify = DateTime.Now;

                await _dataContext.HtThamSoHeThongs.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Create {ThamSoHeThongConstant.CachePrefix} success: {JsonSerializer.Serialize(entity)}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới tham số hệ thống mã: {entity.IdThamSo}",
                    ObjectCode = ThamSoHeThongConstant.CachePrefix,
                    ObjectId = entity.IdThamSo
                });

                //Xóa cache
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey());
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey(model.IdThamSo));

                return Unit.Value;
            }
        }
    }
}
