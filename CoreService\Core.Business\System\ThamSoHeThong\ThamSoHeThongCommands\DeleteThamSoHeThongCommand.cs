using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class DeleteThamSoHeThongCommand : IRequest<Unit>
    {
        public string IdThamSo { get; set; }
        public int IdPh {  get; set; }

        /// <summary>
        /// Xóa tham số hệ thống
        /// </summary>
        /// <param name="idThamSo">ID tham số hệ thống cần xóa</param>
        public DeleteThamSoHeThongCommand(string idThamSo, int idPh)
        {
            IdThamSo = idThamSo;
            IdPh = idPh;
        }

        public class Handler : IRequestHandler<DeleteThamSoHeThongCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(SystemDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteThamSoHeThongCommand request, CancellationToken cancellationToken)
            {
                var idThamSo = request.IdThamSo;
                var idPh = request.IdPh;

                Log.Information($"Delete {ThamSoHeThongConstant.CachePrefix}: {idThamSo}");

                var entity = await _dataContext.HtThamSoHeThongs.FirstOrDefaultAsync(x => x.IdThamSo == idThamSo && x.IdPh == idPh);
                if (entity == null)
                {
                    throw new ArgumentException($"{_localizer["tham-so-he-thong.not-found"]}");
                }

                _dataContext.HtThamSoHeThongs.Remove(entity);
                await _dataContext.SaveChangesAsync();

                Log.Information($"Delete {ThamSoHeThongConstant.CachePrefix} success: {idThamSo}");
                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa tham số hệ thống mã: {entity.IdThamSo}",
                    ObjectCode = ThamSoHeThongConstant.CachePrefix,
                    ObjectId = entity.IdThamSo
                });

                //Xóa cache
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey());
                _cacheService.Remove(ThamSoHeThongConstant.BuildCacheKey(entity.IdThamSo));

                return Unit.Value;
            }
        }
    }
}
