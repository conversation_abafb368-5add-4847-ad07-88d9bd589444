using Core.Data;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetThamSoHeThongByIdQuery : IRequest<ThamSoHeThongModel>
    {
        public string IdThamSo { get; set; }
        public int IdPh { get; set; }
        public bool Active { get; set; }


        /// <summary>
        /// Lấy thông tin tham số hệ thống theo ID
        /// </summary>
        /// <param name="idThamSo">ID tham số hệ thống</param>
        public GetThamSoHeThongByIdQuery(string idThamSo = "", int idPh = 0, bool active = false)
        {
            IdThamSo = idThamSo;
            IdPh = idPh;
            Active = active;
        }

        public class Handler : IRequestHandler<GetThamSoHeThongByIdQuery, ThamSoHeThongModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<ThamSoHeThongModel> Handle(GetThamSoHeThongByIdQuery request, CancellationToken cancellationToken)
            {
                var idPh = request.IdPh;
                var idThamSo = request.IdThamSo;
                var active = request.Active;

                string cacheKey = ThamSoHeThongConstant.BuildCacheKey(idThamSo);
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.HtThamSoHeThongs.FirstOrDefaultAsync(x => x.IdThamSo == idThamSo);
                    return AutoMapperUtils.AutoMap<HtThamSoHeThong, ThamSoHeThongModel>(entity);
                });

                if (idPh > 0 && item.IdPh != idPh)
                    return null;
                 
                if (active && !item.Active)
                    return null;
                    
                return item;
            }
        }
    }
}
