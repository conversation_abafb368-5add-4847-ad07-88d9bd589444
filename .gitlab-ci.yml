image: thienanunisoft/net-sdk:9.0

services:
  - docker:dind

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_TLS_CERTDIR: ""
  DOCKER_DRIVER: overlay2

  # system-api
  IMAGE_NAME_TAG_TEST: thienanunisoft/system-api:test
  IMAGE_NAME_TAG_LATEST: thienanunisoft/system-api:latest
  DOCKER_FILE: Dockerfile/dockerfile_system_api
  MINIO_PATH: uni-system
  ZIP_FILE_NAME: uni-system-api.zip

  # web-api-gw
  IMAGE_NAME_TAG_WEB_API_GW_TEST: thienanunisoft/web-api-gw:test
  IMAGE_NAME_TAG_WEB_API_GW_LATEST: thienanunisoft/web-api-gw:latest
  DOCKER_FILE_WEB_API_GW: Dockerfile/dockerfile_web_api_gw_build
  MINIO_PATH_NAME_WEB_API_GW: web-api-gw
  ZIP_FILE_NAME_WEB_API_GW: web-api-gw.zip

stages:
  - secrets-scan
  - build

secrets_scan:
  image: zricethezav/gitleaks:latest
  stage: secrets-scan
  only:
    - develop
  script:
    - echo "Running GitLeaks scan..."
    - gitleaks detect --config=.gitleaks.toml --report-path=gitleaks-report.json --exit-code 0
    - |
      if grep -q '"rule_id"' gitleaks-report.json; then
        echo "⚠️ GitLeaks found potential secrets. Sending alert to Slack..."
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"🚨 *GitLeaks cảnh báo:* Phát hiện thông tin nhạy cảm trong branch *$CI_COMMIT_REF_NAME*\n🔗 Dự án: $CI_PROJECT_NAME\n🧑‍💻 Người đẩy: $GITLAB_USER_NAME\n🔍 Commit: $CI_COMMIT_SHORT_SHA\"}" \
          $SLACK_WEBHOOK_URL
      else
        echo "✅ Không phát hiện secret nào."
      fi
  allow_failure: true

# System API
# Build image and push to Docker Hub branch test
build_and_push_test:
  stage: build

  before_script:
  - docker --version  # 🧪 Kiểm tra docker đã hoạt động
  - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
  - echo "Logging into Docker Hub..."
  - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
  - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
  - docker pull $IMAGE_NAME_TAG_TEST || true

  script:
    # Build ứng dụng .NET
    - dotnet restore
    - dotnet publish ./APIService/System.API/System.API.csproj -c Release -o ./system-api-publish -f net8.0
    - echo "Build .NET project completed."

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_TEST . -f Dockerfile/dockerfile_system_api_build
    - docker push $IMAGE_NAME_TAG_TEST
    - echo "Docker image pushed to Docker Hub."

    - echo "Uploading files via ncftpput..."
    - ncftpput -R -v -u "$FTP_USER" -p "$FTP_PASS" "$FTP_URL" /UniSoftSystemAPI/build/ ./system-api-publish/

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: shared-dotnet-cache
    paths:
      - ~/.nuget/packages/

  only:
    - test

build_and_push_prod:
  stage: build

  before_script:
  - docker --version  # 🧪 Kiểm tra docker đã hoạt động
  - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
  - echo "Logging into Docker Hub..."
  - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
  - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
  - docker pull $IMAGE_NAME_TAG_LATEST || true

  script:
    # Build ứng dụng .NET
    - dotnet restore
    - dotnet publish ./APIService/System.API/System.API.csproj -c Release -o ./system-api-publish -f net8.0
    - echo "Build .NET project completed."

    # Tạo file zip từ thư mục ./system-api-publish
    - zip -r $ZIP_FILE_NAME ./system-api-publish

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_LATEST . -f Dockerfile/dockerfile_system_api_build
    - docker push $IMAGE_NAME_TAG_LATEST
    - echo "Docker image pushed to Docker Hub."

    # Copy file vào MinIO
    - echo "Copying $ZIP_FILE_NAME to MinIO..."
    - mc cp $ZIP_FILE_NAME $MINIO_ALIAS/$MINIO_BUCKET/$MINIO_PATH/
    
    # Upload file đã build lên server qua FTP
    # Xóa file appsettings.json nếu có
    - rm -f ./system-api-publish/appsettings.json
    
    # Xóa tất cả các file (kể cả trong thư mục con) cũ hơn 1/1/2025
    - find ./system-api-publish/ -type f ! -newermt "2025-01-01" -delete
    
    # Xóa tất cả các file ngoại trừ .dll
    - find ./system-api-publish/ -type f ! \( -name "*.dll" -o -name "*.json" \) -delete

    # Xóa các thư mục rỗng
    - find ./system-api-publish/ -type d -empty -delete

    # Tạo VersionID dạng yyyyMMddHH
    - VERSION_ID=$(date +"%Y%m%d%H")

    # Ghi nội dung file update
    - |
      cat <<EOF > ./system-api-publish/UnisoftServerUpdate.txt
      [UNISOFT_UPDATE]
      VersionID=$VERSION_ID
      EOF

    - echo "Uploading files via ncftpput..."
    - ncftpput -R -v -u "$FTP_USER" -p "$FTP_PASS" "$FTP_URL" /unisoft-deploy/UniSystemAPI/ ./system-api-publish/*

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: shared-dotnet-cache
    paths:
      - ~/.nuget/packages/

  only:
    - master

# Web API Gateway
# Build image and push to Docker Hub branch test
build_and_push_test_web_api_gw:
  stage: build

  before_script:
  - docker --version  # 🧪 Kiểm tra docker đã hoạt động
  - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
  - echo "Logging into Docker Hub..."
  - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
  - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
  - docker pull $IMAGE_NAME_TAG_WEB_API_GW_TEST || true

  script:
    # Build ứng dụng .NET
    - dotnet restore
    - dotnet publish ./APIGateway/WebAPIGateway/WebAPIGateway.csproj -c Release -o ./web-api-gw-publish -f net8.0
    - echo "Build Web Api Gateway project completed."

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_WEB_API_GW_TEST . -f $DOCKER_FILE_WEB_API_GW
    - docker push $IMAGE_NAME_TAG_WEB_API_GW_TEST
    - echo "Docker image pushed to Docker Hub."

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build web-api-gw *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build web-api-gw *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: shared-dotnet-cache
    paths:
      - ~/.nuget/packages/

  rules:
    # Chạy job này nếu commit message có chứa từ khóa "build-web-api-gw" hoặc "build-gw" và thuộc nhánh test
    - if: '$CI_COMMIT_BRANCH == "test" && ($CI_COMMIT_MESSAGE =~ /build-web-api-gw/ || $CI_COMMIT_MESSAGE =~ /build-gw/)'

build_and_push_prod_web_api_gw:
  stage: build

  before_script:
  - docker --version  # 🧪 Kiểm tra docker đã hoạt động
  - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
  - echo "Logging into Docker Hub..."
  - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
  - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
  - docker pull $IMAGE_NAME_TAG_WEB_API_GW_LATEST || true

  script:
    # Build ứng dụng .NET
    - dotnet restore
    - dotnet publish ./APIGateway/WebAPIGateway/WebAPIGateway.csproj -c Release -o ./web-api-gw-publish -f net8.0
    - echo "Build Web Api Gateway project completed."

    # Tạo file zip từ thư mục ./web-api-gw-publish
    - zip -r $ZIP_FILE_NAME_WEB_API_GW ./web-api-gw-publish

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_WEB_API_GW_LATEST . -f $DOCKER_FILE_WEB_API_GW
    - docker push $IMAGE_NAME_TAG_WEB_API_GW_LATEST
    - echo "Docker image pushed to Docker Hub."

    # Copy file vào MinIO
    - echo "Copying $ZIP_FILE_NAME_WEB_API_GW to MinIO..."
    - mc cp $ZIP_FILE_NAME_WEB_API_GW $MINIO_ALIAS/$MINIO_BUCKET/$MINIO_PATH_NAME_WEB_API_GW/
    
    # Upload file đã build lên server qua FTP
    # Xóa file appsettings.json nếu có
    - rm -f ./web-api-gw-publish/appsettings.json
    
    # Xóa tất cả các file (kể cả trong thư mục con) cũ hơn 1/1/2025
    - find ./web-api-gw-publish/ -type f ! -newermt "2025-01-01" -delete
    
    # Xóa tất cả các file ngoại trừ .dll
    - find ./web-api-gw-publish/ -type f ! \( -name "*.dll" -o -name "*.json" \) -delete

    # Xóa các thư mục rỗng
    - find ./web-api-gw-publish/ -type d -empty -delete

    # Tạo VersionID dạng yyyyMMddHH
    - VERSION_ID=$(date +"%Y%m%d%H")

    # Ghi nội dung file update
    - |
      cat <<EOF > ./web-api-gw-publish/UnisoftServerUpdate.txt
      [UNISOFT_UPDATE]
      VersionID=$VERSION_ID
      EOF

    - echo "Uploading files via ncftpput..."
    - ncftpput -R -v -u "$FTP_USER" -p "$FTP_PASS" "$FTP_URL" /unisoft-deploy/UniSoftGatewayAPI/ ./web-api-gw-publish/*

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build web-api-gw *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build web-api-gw *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: shared-dotnet-cache
    paths:
      - ~/.nuget/packages/

  rules:
    # Chạy job này nếu commit message có chứa từ khóa "build-web-api-gw" hoặc "build-gw" và thuộc nhánh master
    - if: '$CI_COMMIT_BRANCH == "master" && ($CI_COMMIT_MESSAGE =~ /build-web-api-gw/ || $CI_COMMIT_MESSAGE =~ /build-gw/)'
